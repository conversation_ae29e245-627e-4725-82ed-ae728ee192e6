defmodule Repobot.SourceFiles.FormComponent.SaveTest do
  use Repobot.DataCase

  alias Repobot.SourceFiles.FormComponent.Save
  alias Repobot.SourceFile

  use Repobot.Test.Fixtures

  describe "Save command for new source files" do
    test "creates a new source file successfully" do
      user = create_user()

      params = %{
        "action" => :new,
        "name" => "test.md",
        "content" => "# Test Content",
        "target_path" => "test.md",
        "is_template" => false,
        "user_id" => user.id,
        "organization_id" => user.default_organization_id,
        "tags" => ["test", "markdown"]
      }

      assert {:ok, %{result: source_file}} = Save.call(params)
      assert source_file.name == "test.md"
      assert source_file.content == "# Test Content"
      assert source_file.target_path == "test.md"
      assert source_file.is_template == false
      assert source_file.user_id == user.id
      assert source_file.organization_id == user.default_organization_id

      # Check tags were created and associated
      tag_names = Enum.map(source_file.tags, & &1.name)
      assert "test" in tag_names
      assert "markdown" in tag_names
    end

    test "creates a new source file with string tags" do
      user = create_user()

      params = %{
        "action" => :new,
        "name" => "config.yml",
        "content" => "key: value",
        "user_id" => user.id,
        "organization_id" => user.default_organization_id,
        "tags" => "config, yaml, settings"
      }

      assert {:ok, %{result: source_file}} = Save.call(params)
      assert source_file.name == "config.yml"
      # Should auto-set from name
      assert source_file.target_path == "config.yml"

      # Check tags were parsed and created
      tag_names = Enum.map(source_file.tags, & &1.name)
      assert "config" in tag_names
      assert "yaml" in tag_names
      assert "settings" in tag_names
    end

    test "validates required fields for new source files" do
      params = %{
        "action" => :new,
        "name" => "test.md"
        # Missing user_id and organization_id
      }

      assert {:error, %{result: changeset}} = Save.call(params)
      assert changeset.errors[:user_id]
      assert changeset.errors[:organization_id]
    end
  end

  describe "Save command for existing source files" do
    test "updates an existing source file successfully" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "old_name.md",
          content: "old content",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      params = %{
        "action" => :edit,
        "source_file" => source_file,
        "name" => "new_name.md",
        "content" => "new content",
        "target_path" => "new_name.md",
        "tags" => ["updated", "test"]
      }

      assert {:ok, %{result: updated_source_file}} = Save.call(params)
      assert updated_source_file.id == source_file.id
      assert updated_source_file.name == "new_name.md"
      assert updated_source_file.content == "new content"
      assert updated_source_file.target_path == "new_name.md"

      # Check tags were updated
      tag_names = Enum.map(updated_source_file.tags, & &1.name)
      assert "updated" in tag_names
      assert "test" in tag_names
    end

    test "prevents updating read-only source files" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "readonly.md",
          content: "readonly content",
          read_only: true,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      params = %{
        "action" => :edit,
        "source_file" => source_file,
        "name" => "new_name.md",
        "content" => "new content"
      }

      assert {:error, %{result: error_message}} = Save.call(params)
      assert error_message =~ "Cannot update read-only source file"
    end

    test "allows updating only read_only flag on read-only source files" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "readonly.md",
          content: "readonly content",
          read_only: true,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      params = %{
        "action" => :edit,
        "source_file" => source_file,
        "read_only" => false
      }

      assert {:ok, %{result: updated_source_file}} = Save.call(params)
      assert updated_source_file.read_only == false
      # Other fields unchanged
      assert updated_source_file.name == "readonly.md"
    end

    test "validates source_file is provided for edit action" do
      params = %{
        "action" => :edit,
        "name" => "test.md"
        # Missing source_file
      }

      assert {:error, %{result: changeset}} = Save.call(params)
      assert changeset.errors[:source_file]
    end
  end

  describe "target_path auto-setting" do
    test "sets target_path to name when not provided" do
      user = create_user()

      params = %{
        "action" => :new,
        "name" => "auto_target.md",
        "user_id" => user.id,
        "organization_id" => user.default_organization_id
      }

      assert {:ok, %{result: source_file}} = Save.call(params)
      assert source_file.target_path == "auto_target.md"
    end

    test "preserves provided target_path" do
      user = create_user()

      params = %{
        "action" => :new,
        "name" => "source.md",
        "target_path" => "custom/path.md",
        "user_id" => user.id,
        "organization_id" => user.default_organization_id
      }

      assert {:ok, %{result: source_file}} = Save.call(params)
      assert source_file.target_path == "custom/path.md"
    end
  end
end
