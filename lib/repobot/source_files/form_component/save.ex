defmodule Repobot.SourceFiles.FormComponent.Save do
  @moduledoc """
  Command for saving source files from the FormComponent.
  Handles both creating new source files and updating existing ones.
  """

  use Repobot.Operation, type: :form

  import Ecto.Query

  alias Repobot.{Tags, Repo}
  alias Repobot.SourceFile

  schema do
    %{
      required(:name) => string(),
      required(:user_id) => string(),
      required(:organization_id) => string(),
      optional(:content) => string(),
      optional(:target_path) => string(),
      optional(:is_template) => any(),
      optional(:tags) => any()
    }
  end

  @impl true
  def execute(source_file, params) when not is_nil(source_file) do
    update_source_file(source_file, params)
  end

  @impl true
  def execute(params) do
    create_source_file(params)
  end

  @impl true
  def validate(params) do
    # Since we're using a plain schema, just return the params
    # The actual validation will happen in the SourceFile.changeset
    params
  end

  # Private functions

  defp create_source_file(params) do
    {tags_input, attrs} = Map.pop(params, :tags, [])
    {content, attrs} = Map.pop(attrs, :content)

    # Process tags from string to list if needed
    tag_names = process_tags_input(tags_input)

    # Use a transaction to ensure both SourceFile and FileContent are created together
    Repo.transaction(fn ->
      # Create the SourceFile first
      case %SourceFile{}
           |> SourceFile.changeset(attrs)
           |> Repo.insert() do
        {:ok, source_file} ->
          # Create FileContent if content is provided
          if content do
            case Repo.insert(%Repobot.FileContent{
                   blob: content,
                   source_file_id: source_file.id
                 }) do
              {:ok, _fc} -> :ok
              {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
            end
          end

          # Get or create tags and associate them with the source file
          source_file_preloaded = Repo.preload(source_file, [:user, :tags])
          tags = Tags.get_or_create_tags(tag_names, source_file_preloaded.user)

          source_file_preloaded
          |> Ecto.Changeset.change()
          |> Ecto.Changeset.put_assoc(:tags, tags)
          |> Repo.update()
          |> case do
            {:ok, source_file_with_tags} ->
              # Reload with content projection
              from(sf in SourceFile,
                left_join: fc in assoc(sf, :file_content),
                where: sf.id == ^source_file_with_tags.id,
                select: %{sf | content: fc.blob}
              )
              |> Repo.one!()
              |> Repo.preload([:tags, :repositories])

            {:error, changeset} ->
              Repo.rollback({:tags_error, changeset})
          end

        {:error, changeset} ->
          Repo.rollback({:source_file_error, changeset})
      end
    end)
    |> case do
      {:ok, source_file} -> {:ok, source_file}
      {:error, {_error_type, changeset}} -> {:error, changeset}
      {:error, reason} -> {:error, reason}
    end
  end

  defp update_source_file(source_file, params) do
    source_file = Repo.preload(source_file, [:user, :tags, :source_repository, :file_content])

    # Prevent updates to read-only source files unless we're only changing the read_only flag itself
    if source_file.read_only do
      changed_fields = Map.keys(params) -- [:read_only, "read_only"]

      if not Enum.empty?(changed_fields) do
        {:error,
         "Cannot update read-only source file. This file is managed by GitHub events from its template repository."}
      else
        # Only read_only field is being changed, allow the update
        changeset = SourceFile.changeset(source_file, params)

        case Repo.update(changeset) do
          {:ok, updated_source_file} ->
            # Reload with content projection
            reloaded_source_file =
              from(sf in SourceFile,
                left_join: fc in assoc(sf, :file_content),
                where: sf.id == ^updated_source_file.id,
                select: %{sf | content: fc.blob}
              )
              |> Repo.one!()
              |> Repo.preload(:tags)

            {:ok, reloaded_source_file}

          error ->
            error
        end
      end
    else
      {content, attrs} = Map.pop(params, :content)
      {tags_input, attrs} = Map.pop(attrs, :tags, [])

      # Process tags from string to list if needed
      tag_names = process_tags_input(tags_input)

      # Use a transaction to ensure both SourceFile and FileContent are updated together
      Repo.transaction(fn ->
        # Update FileContent if content is provided
        if content do
          case source_file.file_content do
            nil ->
              # Create new FileContent
              case Repo.insert(%Repobot.FileContent{
                     blob: content,
                     source_file_id: source_file.id
                   }) do
                {:ok, _fc} -> :ok
                {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
              end

            file_content ->
              # Update existing FileContent
              case Repo.update(Ecto.Changeset.change(file_content, blob: content)) do
                {:ok, _fc} -> :ok
                {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
              end
          end
        end

        # Update the SourceFile
        changeset = SourceFile.changeset(source_file, attrs)

        case Repo.update(changeset) do
          {:ok, updated_source_file} ->
            # Update tags if provided
            if tag_names && length(tag_names) > 0 do
              tags = Tags.get_or_create_tags(tag_names, source_file.user)

              case updated_source_file
                   |> Ecto.Changeset.change()
                   |> Ecto.Changeset.put_assoc(:tags, tags)
                   |> Repo.update() do
                {:ok, source_file_with_tags} -> source_file_with_tags
                {:error, changeset} -> Repo.rollback({:tags_error, changeset})
              end
            else
              updated_source_file
            end

          {:error, changeset} ->
            Repo.rollback({:source_file_error, changeset})
        end
      end)
      |> case do
        {:ok, updated_source_file} ->
          # Reload with content projection and preserve associations
          reloaded_source_file =
            from(sf in SourceFile,
              left_join: fc in assoc(sf, :file_content),
              where: sf.id == ^updated_source_file.id,
              select: %{sf | content: fc.blob}
            )
            |> Repo.one!()
            |> Repo.preload([:tags, :repositories])

          {:ok, reloaded_source_file}

        {:error, {_error_type, changeset}} ->
          {:error, changeset}

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  # Helper functions

  defp process_tags_input(tags) when is_binary(tags) do
    tags
    |> String.split(",")
    |> Enum.map(&String.trim/1)
    |> Enum.reject(&(&1 == ""))
  end

  defp process_tags_input(tags) when is_list(tags), do: tags
  defp process_tags_input(_), do: []
end
