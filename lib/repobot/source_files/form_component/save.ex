defmodule Repobot.SourceFiles.FormComponent.Save do
  @moduledoc """
  Command for saving source files from the FormComponent.
  Handles both creating new source files and updating existing ones.
  """

  use Repobot.Operation, type: :form

  import Ecto.Query
  import Ecto.Changeset

  alias Repobot.{Tags, Repo}
  alias Repobot.SourceFile

  schema do
    %{
      required(:name) => string(),
      required(:user_id) => string(),
      required(:organization_id) => string(),
      optional(:source_file) => any(),
      optional(:content) => string(),
      optional(:target_path) => string(),
      optional(:is_template) => bool(),
      optional(:template_vars) => map(),
      optional(:category_id) => string(),
      optional(:source_repository_id) => string(),
      optional(:repository_file_id) => string(),
      optional(:read_only) => bool(),
      optional(:tags) => any()
    }
  end

  @impl true
  def execute(%{source_file: source_file} = params) when not is_nil(source_file) do
    update_source_file(source_file, params)
  end

  def execute(params) do
    create_source_file(params)
  end

  # Override changeset to handle Ecto fields properly
  def changeset(params) do
    # Extract Ecto fields from params
    ecto_fields = [
      :name,
      :content,
      :target_path,
      :is_template,
      :template_vars,
      :user_id,
      :organization_id,
      :category_id,
      :source_repository_id,
      :repository_file_id,
      :read_only
    ]

    ecto_params = Map.take(params, ecto_fields)

    %Repobot.SourceFile{}
    |> cast(ecto_params, ecto_fields)
    # Store original params for access to non-Ecto fields
    |> Map.put(:params, params)
  end

  @impl true
  def validate(changeset) do
    changeset
    |> validate_required([:name, :user_id, :organization_id])
    |> validate_source_file_for_edit()
    |> validate_not_read_only_for_edit()
    |> maybe_set_target_path()
    |> validate_required([:target_path])
    |> set_default_values()
    |> process_tags()
  end

  # Private functions

  defp create_source_file(params) do
    {tag_names, attrs} = Map.pop(params, :tags, [])
    {content, attrs} = Map.pop(attrs, :content)

    # Use a transaction to ensure both SourceFile and FileContent are created together
    Repo.transaction(fn ->
      # Create the SourceFile first
      case %SourceFile{}
           |> SourceFile.changeset(attrs)
           |> Repo.insert() do
        {:ok, source_file} ->
          # Create FileContent if content is provided
          if content do
            case Repo.insert(%Repobot.FileContent{
                   blob: content,
                   source_file_id: source_file.id
                 }) do
              {:ok, _fc} -> :ok
              {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
            end
          end

          # Get or create tags and associate them with the source file
          source_file_preloaded_user = Repo.preload(source_file, :user)
          tags = Tags.get_or_create_tags(tag_names, source_file_preloaded_user.user)

          source_file
          |> Ecto.Changeset.change()
          |> Ecto.Changeset.put_assoc(:tags, tags)
          |> Repo.update()
          |> case do
            {:ok, source_file_with_tags} ->
              # Reload with content projection
              from(sf in SourceFile,
                left_join: fc in assoc(sf, :file_content),
                where: sf.id == ^source_file_with_tags.id,
                select: %{sf | content: fc.blob}
              )
              |> Repo.one!()
              |> Repo.preload([:tags, :repositories])

            {:error, changeset} ->
              Repo.rollback({:tags_error, changeset})
          end

        {:error, changeset} ->
          Repo.rollback({:source_file_error, changeset})
      end
    end)
    |> case do
      {:ok, source_file} -> {:ok, source_file}
      {:error, {_error_type, changeset}} -> {:error, changeset}
      {:error, reason} -> {:error, reason}
    end
  end

  defp update_source_file(source_file, params) do
    source_file = Repo.preload(source_file, [:user, :tags, :source_repository, :file_content])

    # Prevent updates to read-only source files unless we're only changing the read_only flag itself
    if source_file.read_only do
      changed_fields = Map.keys(params) -- [:read_only, "read_only"]

      if not Enum.empty?(changed_fields) do
        {:error,
         "Cannot update read-only source file. This file is managed by GitHub events from its template repository."}
      else
        # Only read_only field is being changed, allow the update
        changeset = SourceFile.changeset(source_file, params)

        case Repo.update(changeset) do
          {:ok, updated_source_file} ->
            # Reload with content projection
            reloaded_source_file =
              from(sf in SourceFile,
                left_join: fc in assoc(sf, :file_content),
                where: sf.id == ^updated_source_file.id,
                select: %{sf | content: fc.blob}
              )
              |> Repo.one!()
              |> Repo.preload(:tags)

            {:ok, reloaded_source_file}

          error ->
            error
        end
      end
    else
      {content, attrs} = Map.pop(params, :content)
      {tag_names, attrs} = Map.pop(attrs, :tags, [])

      # Use a transaction to ensure both SourceFile and FileContent are updated together
      Repo.transaction(fn ->
        # Update FileContent if content is provided
        if content do
          case source_file.file_content do
            nil ->
              # Create new FileContent
              case Repo.insert(%Repobot.FileContent{
                     blob: content,
                     source_file_id: source_file.id
                   }) do
                {:ok, _fc} -> :ok
                {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
              end

            file_content ->
              # Update existing FileContent
              case Repo.update(Ecto.Changeset.change(file_content, blob: content)) do
                {:ok, _fc} -> :ok
                {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
              end
          end
        end

        # Update the SourceFile
        changeset = SourceFile.changeset(source_file, attrs)

        case Repo.update(changeset) do
          {:ok, updated_source_file} ->
            # Update tags if provided
            if tag_names && length(tag_names) > 0 do
              tags = Tags.get_or_create_tags(tag_names, source_file.user)

              case updated_source_file
                   |> Ecto.Changeset.change()
                   |> Ecto.Changeset.put_assoc(:tags, tags)
                   |> Repo.update() do
                {:ok, source_file_with_tags} -> source_file_with_tags
                {:error, changeset} -> Repo.rollback({:tags_error, changeset})
              end
            else
              updated_source_file
            end

          {:error, changeset} ->
            Repo.rollback({:source_file_error, changeset})
        end
      end)
      |> case do
        {:ok, updated_source_file} ->
          # Reload with content projection and preserve associations
          reloaded_source_file =
            from(sf in SourceFile,
              left_join: fc in assoc(sf, :file_content),
              where: sf.id == ^updated_source_file.id,
              select: %{sf | content: fc.blob}
            )
            |> Repo.one!()
            |> Repo.preload([:tags, :repositories])

          {:ok, reloaded_source_file}

        {:error, {_error_type, changeset}} ->
          {:error, changeset}

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  # Validation helpers

  defp validate_source_file_for_edit(changeset) do
    # This validation is no longer needed since we determine create vs update
    # based on the presence of source_file in the execute function
    changeset
  end

  defp validate_not_read_only_for_edit(changeset) do
    # Only validate for updates (when source_file is present)
    source_file = changeset.params[:source_file]

    if source_file && source_file.read_only do
      # Check if we're only changing the read_only flag
      changed_fields = Map.keys(changeset.changes) -- [:read_only]

      if not Enum.empty?(changed_fields) do
        add_error(
          changeset,
          :base,
          "Cannot update read-only source file. This file is managed by GitHub events."
        )
      else
        changeset
      end
    else
      changeset
    end
  end

  defp maybe_set_target_path(changeset) do
    name = get_field(changeset, :name)
    target_path = get_field(changeset, :target_path)

    if name && (!target_path || target_path == "") do
      put_change(changeset, :target_path, name)
    else
      changeset
    end
  end

  defp set_default_values(changeset) do
    changeset
    |> maybe_put_default(:is_template, false)
    |> maybe_put_default(:read_only, false)
    |> maybe_put_default(:template_vars, %{})
    |> maybe_put_default(:category_id, nil)
    |> maybe_put_default(:source_repository_id, nil)
    |> maybe_put_default(:repository_file_id, nil)
  end

  defp maybe_put_default(changeset, field, default_value) do
    if get_field(changeset, field) == nil do
      put_change(changeset, field, default_value)
    else
      changeset
    end
  end

  defp process_tags(changeset) do
    # Access tags from original params since it's not an Ecto field
    case changeset.params[:tags] do
      tags when is_binary(tags) ->
        tag_names =
          tags
          |> String.split(",")
          |> Enum.map(&String.trim/1)
          |> Enum.reject(&(&1 == ""))

        put_change(changeset, :tags, tag_names)

      tags when is_list(tags) ->
        put_change(changeset, :tags, tags)

      _ ->
        put_change(changeset, :tags, [])
    end
  end
end
